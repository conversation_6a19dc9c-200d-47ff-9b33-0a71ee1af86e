"use client";

import Image from "next/image";
import { Link } from "@/i18n/navigation";
import { usePathname, useRouter } from "@/i18n/navigation";
import { useLocale, useTranslations } from "next-intl";
import { Home, Users, LogOut, Globe, PanelLeft } from "lucide-react";
import { cn } from "@/lib/utils";
import { dtcAssets } from "@/lib/assets";
import { useAuth } from "@/hooks/useAuth";
import { useUser } from "@/hooks/useUser";
import { toast } from "sonner";
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarTrigger,
} from "@/components/ui/sidebar";
import { Button } from "@/components/ui/button";

interface NavigationItem {
  label: string;
  href: string;
  icon: React.ComponentType<{ className?: string }>;
  roles: string[];
}

interface DtcSidebarProps {
  className?: string;
}

export function DtcSidebar({ className }: DtcSidebarProps) {
  const pathname = usePathname();
  const router = useRouter();
  const locale = useLocale();
  const t = useTranslations("nav");
  const { signOut } = useAuth();
  const { user } = useUser();

  // Navigation items based on user roles
  const navigationItems: NavigationItem[] = [
    {
      label: t("home"),
      href: "/dashboard",
      icon: Home,
      roles: ["Admin", "DTC"],
    },
    {
      label: t("userManagement"),
      href: "/dashboard/users",
      icon: Users,
      roles: ["Admin"],
    },
  ];

  // Filter navigation items based on user role
  const filteredNavItems = navigationItems.filter(item =>
    user?.role && item.roles.includes(user.role)
  );

  const handleSignOut = async () => {
    try {
      await signOut();
      toast.success("Signed out successfully");
      router.push("/login");
    } catch (error) {
      console.error("Sign out error:", error);
      toast.error("Failed to sign out");
    }
  };

  const switchLanguage = () => {
    const newLocale = locale === "en" ? "ar" : "en";
    router.replace(pathname, { locale: newLocale });
  };

  return (
    <Sidebar className={cn("border-0 bg-gradient-to-b from-primary to-primary-deep", className)}>
      <SidebarHeader className="p-6 border-b border-white/10">
        {/* Logo Section */}
        <div className="flex items-center justify-center mb-6">
          <div className="bg-white/15 backdrop-blur-sm rounded-lg p-3 border border-white/20 shadow-xl">
            <Image
              src={dtcAssets.logoBlack}
              alt="DTC Logo"
              width={100}
              height={32}
              className="object-contain brightness-0 invert"
              priority
            />
          </div>
        </div>

        {/* User Info Section */}
        <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4 border border-white/20 shadow-lg">
          <div className="flex items-center space-x-3">
            {user?.photoURL ? (
              <div className="relative">
                <Image
                  src={user.photoURL}
                  alt="User Avatar"
                  width={48}
                  height={48}
                  className="rounded-full border-2 border-white/40 shadow-md"
                />
                <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-accent rounded-full border-2 border-white shadow-sm"></div>
              </div>
            ) : (
              <div className="relative">
                <div className="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center border-2 border-white/40 shadow-md">
                  <span className="text-white font-bold text-lg font-display">
                    {user?.displayName?.charAt(0) || user?.email?.charAt(0) || "U"}
                  </span>
                </div>
                <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-accent rounded-full border-2 border-white shadow-sm"></div>
              </div>
            )}
            <div className="flex-1 min-w-0">
              <p className="text-white font-semibold text-base font-display truncate">
                {user?.displayName || user?.email?.split('@')[0] || "User"}
              </p>
              <div className="flex items-center mt-1">
                <div className="px-3 py-1 bg-accent/20 rounded-full border border-accent/30">
                  <span className="text-xs font-medium text-white">
                    {user?.role || "User"}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </SidebarHeader>

      <SidebarContent className="flex-1 px-4 py-6">
        <nav className="space-y-2">
          {filteredNavItems.map((item) => {
            const isActive = pathname === item.href;
            return (
              <Link
                key={item.href}
                href={item.href}
                className={cn(
                  "flex items-center space-x-3 px-4 py-3 rounded-lg transition-all duration-200",
                  "text-white/90 hover:text-white hover:bg-white/15",
                  "border border-transparent hover:border-white/20",
                  "group relative overflow-hidden",
                  isActive && "bg-white/20 text-white border-white/30 shadow-lg"
                )}
              >
                <item.icon className={cn(
                  "w-5 h-5 transition-transform duration-200",
                  "group-hover:scale-110",
                  isActive && "scale-110"
                )} />
                <span className="font-medium text-sm">{item.label}</span>
                {isActive && (
                  <div className="absolute inset-0 bg-gradient-to-r from-white/5 to-transparent pointer-events-none" />
                )}
              </Link>
            );
          })}
        </nav>
      </SidebarContent>

      <SidebarFooter className="p-4 border-t border-white/10 space-y-2">
        {/* Language Switcher */}
        <Button
          variant="ghost"
          size="sm"
          onClick={switchLanguage}
          className="w-full justify-start text-white/90 hover:text-white hover:bg-white/15 border border-transparent hover:border-white/20 rounded-lg transition-all duration-200 h-10"
        >
          <Globe className="w-4 h-4 mr-3" />
          <span className="text-sm font-medium">{t("switchLanguage")}</span>
        </Button>

        {/* Sidebar Minimize Button */}
        <SidebarTrigger className="w-full justify-start text-white/90 hover:text-white hover:bg-white/15 border border-transparent hover:border-white/20 rounded-lg transition-all duration-200 h-10">
          <PanelLeft className="w-4 h-4 mr-3" />
          <span className="text-sm font-medium">{t("minimize")}</span>
        </SidebarTrigger>

        {/* Sign Out Button */}
        <Button
          variant="ghost"
          size="sm"
          onClick={handleSignOut}
          className="w-full justify-start text-white/90 hover:text-white hover:bg-white/15 border border-transparent hover:border-white/20 rounded-lg transition-all duration-200 h-10"
        >
          <LogOut className="w-4 h-4 mr-3" />
          <span className="text-sm font-medium">{t("signOut")}</span>
        </Button>
      </SidebarFooter>
    </Sidebar>
  );
}