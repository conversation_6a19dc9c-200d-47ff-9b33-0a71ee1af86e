"use client";

import Image from "next/image";
import Link from "next/link";
import { usePathname, useRouter } from "next/navigation";
import { useLocale } from "next-intl";
import { Home, Users, LogOut, Menu, ChevronRight } from "lucide-react";
import { cn } from "@/lib/utils";
import { dtcAssets } from "@/lib/assets";
import { useAuth } from "@/hooks/useAuth";
import { useUser } from "@/hooks/useUser";
import BrandButton from "@/components/ui/BrandButton";
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet";
import { useState } from "react";
import { toast } from "sonner";

interface NavigationItem {
  label: string;
  href: string;
  icon: React.ComponentType<{ className?: string }>;
  roles: string[]; // Roles that can access this item
}

interface DtcSidebarProps {
  className?: string;
}

export function DtcSidebar({ className }: DtcSidebarProps) {
  const pathname = usePathname();
  const router = useRouter();
  const locale = useLocale();
  const { signOut } = useAuth();
  const { user } = useUser();
  const [isMobileOpen, setIsMobileOpen] = useState(false);

  // Navigation items based on user roles
  const navigationItems: NavigationItem[] = [
    {
      label: "Dashboard",
      href: `/${locale}/dashboard`,
      icon: Home,
      roles: ["Admin", "DTC"], // Both roles can access
    },
    {
      label: "User Management",
      href: `/${locale}/dashboard/users`,
      icon: Users,
      roles: ["Admin"], // Only Admin can access
    },
  ];

  // Filter navigation items based on user role
  const filteredNavItems = navigationItems.filter(item =>
    user?.role && item.roles.includes(user.role)
  );

  const handleSignOut = async () => {
    try {
      await signOut();
      toast.success("Signed out successfully");
      router.push(`/${locale}/login`);
    } catch (error) {
      console.error("Sign out error:", error);
      toast.error("Failed to sign out");
    }
  };

  const SidebarContent = () => (
    <div className="flex flex-col h-full bg-gradient-to-b from-[--color-primary] to-[--color-primary-deep] text-white">
      {/* Logo Section */}
      <div className="flex items-center justify-center px-8 py-12">
        <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20">
          <Image
            src={dtcAssets.logoBlack}
            alt="DTC Logo"
            width={140}
            height={48}
            className="object-contain brightness-0 invert"
            priority
          />
        </div>
      </div>

      {/* User Info Section */}
      <div className="px-6 pb-8">
        <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20">
          <div className="flex items-center space-x-4">
            {user?.photoURL ? (
              <div className="relative">
                <Image
                  src={user.photoURL}
                  alt="User Avatar"
                  width={48}
                  height={48}
                  className="rounded-full border-2 border-white/30"
                />
                <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-[--color-accent] rounded-full border-2 border-white"></div>
              </div>
            ) : (
              <div className="relative">
                <div className="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center border-2 border-white/30">
                  <span className="text-white font-bold text-lg font-display">
                    {user?.displayName?.charAt(0) || user?.email?.charAt(0) || "U"}
                  </span>
                </div>
                <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-[--color-accent] rounded-full border-2 border-white"></div>
              </div>
            )}
            <div className="flex-1 min-w-0">
              <p className="text-white font-semibold text-base font-display truncate">
                {user?.displayName || user?.email?.split('@')[0] || "User"}
              </p>
              <div className="flex items-center space-x-2 mt-1">
                <div className="px-2 py-1 bg-white/20 rounded-lg">
                  <span className="text-xs font-medium text-white/90">
                    {user?.role || "User"}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Navigation Section */}
      <nav className="flex-1 px-6 space-y-3">
        <div className="text-xs font-semibold text-white/60 uppercase tracking-wider px-4 mb-4">
          Navigation
        </div>
        {filteredNavItems.map((item) => {
          const isActive = pathname === item.href;
          const Icon = item.icon;

          return (
            <Link
              key={item.href}
              href={item.href}
              onClick={() => setIsMobileOpen(false)}
              className={cn(
                "group flex items-center justify-between px-4 py-4 rounded-xl transition-all duration-300",
                "hover:bg-white/10 hover:backdrop-blur-sm hover:border-white/20 border border-transparent",
                isActive
                  ? "bg-white/15 backdrop-blur-sm border-white/30 shadow-lg"
                  : "hover:translate-x-1"
              )}
            >
              <div className="flex items-center space-x-4">
                <div className={cn(
                  "p-2 rounded-lg transition-all duration-300",
                  isActive
                    ? "bg-white/20 shadow-lg"
                    : "bg-white/10 group-hover:bg-white/15"
                )}>
                  <Icon
                    className={cn(
                      "w-5 h-5 transition-all duration-300",
                      isActive
                        ? "text-white"
                        : "text-white/80 group-hover:text-white"
                    )}
                    strokeWidth={1.5}
                  />
                </div>
                <span className={cn(
                  "font-medium text-base transition-all duration-300",
                  isActive
                    ? "text-white font-semibold"
                    : "text-white/90 group-hover:text-white"
                )}>
                  {item.label}
                </span>
              </div>
              <ChevronRight
                className={cn(
                  "w-4 h-4 transition-all duration-300",
                  isActive
                    ? "text-white opacity-100"
                    : "text-white/60 opacity-0 group-hover:opacity-100 group-hover:translate-x-1"
                )}
                strokeWidth={1.5}
              />
            </Link>
          );
        })}
      </nav>

      {/* Sign Out Section */}
      <div className="p-6">
        <BrandButton
          onClick={handleSignOut}
          variant="ghost"
          className="w-full justify-start space-x-4 px-4 py-4 text-white/90 hover:text-white hover:bg-white/10 border border-white/20 hover:border-white/30 transition-all duration-300"
        >
          <div className="p-2 rounded-lg bg-white/10">
            <LogOut className="w-5 h-5" strokeWidth={1.5} />
          </div>
          <span className="font-medium text-base">Sign Out</span>
        </BrandButton>
      </div>
    </div>
  );

  return (
    <>
      {/* Mobile Trigger */}
      <div className="lg:hidden">
        <Sheet open={isMobileOpen} onOpenChange={setIsMobileOpen}>
          <SheetTrigger asChild>
            <BrandButton
              variant="primary"
              className="fixed top-6 left-6 z-50 shadow-xl border-0 w-12 h-12 p-0 rounded-xl"
            >
              <Menu className="w-6 h-6" strokeWidth={1.5} />
            </BrandButton>
          </SheetTrigger>
          <SheetContent side="left" className="p-0 w-80 border-0">
            <SidebarContent />
          </SheetContent>
        </Sheet>
      </div>

      {/* Desktop Sidebar */}
      <div className={cn("hidden lg:flex lg:w-80 lg:flex-shrink-0 lg:shadow-2xl lg:relative lg:z-10", className)}>
        <SidebarContent />
      </div>
    </>
  );
}
