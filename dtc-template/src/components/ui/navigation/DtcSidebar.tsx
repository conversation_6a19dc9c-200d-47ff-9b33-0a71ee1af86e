"use client";

import Image from "next/image";
import Link from "next/link";
import { usePathname, useRouter } from "next/navigation";
import { useLocale } from "next-intl";
import { Home, Users, LogOut, Globe, PanelLeft } from "lucide-react";
import { cn } from "@/lib/utils";
import { dtcAssets } from "@/lib/assets";
import { useAuth } from "@/hooks/useAuth";
import { useUser } from "@/hooks/useUser";
import { toast } from "sonner";
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarTrigger,
} from "@/components/ui/sidebar";
import { Button } from "@/components/ui/button";

interface NavigationItem {
  label: string;
  href: string;
  icon: React.ComponentType<{ className?: string }>;
  roles: string[];
}

interface DtcSidebarProps {
  className?: string;
}

export function DtcSidebar({ className }: DtcSidebarProps) {
  const pathname = usePathname();
  const router = useRouter();
  const locale = useLocale();
  const { signOut } = useAuth();
  const { user } = useUser();

  // Navigation items based on user roles
  const navigationItems: NavigationItem[] = [
    {
      label: "Home",
      href: `/${locale}/dashboard`,
      icon: Home,
      roles: ["Admin", "DTC"],
    },
    {
      label: "User Management",
      href: `/${locale}/dashboard/users`,
      icon: Users,
      roles: ["Admin"],
    },
  ];

  // Filter navigation items based on user role
  const filteredNavItems = navigationItems.filter(item =>
    user?.role && item.roles.includes(user.role)
  );

  const handleSignOut = async () => {
    try {
      await signOut();
      toast.success("Signed out successfully");
      router.push(`/${locale}/login`);
    } catch (error) {
      console.error("Sign out error:", error);
      toast.error("Failed to sign out");
    }
  };

  const switchLanguage = () => {
    const newLocale = locale === "en" ? "ar" : "en";
    router.replace(pathname.replace(`/${locale}`, `/${newLocale}`));
  };

  return (
    <Sidebar className={cn("border-0", className)}>
      <SidebarHeader className="p-0">
        {/* Logo Section */}
        <div className="flex items-center justify-center px-6 py-8 bg-gradient-to-br from-primary via-primary to-primary-deep">
          <div className="bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/20 shadow-lg">
            <Image
              src={dtcAssets.logoBlack}
              alt="DTC Logo"
              width={120}
              height={40}
              className="object-contain brightness-0 invert"
              priority
            />
          </div>
        </div>

        {/* User Info Section */}
        <div className="px-4 pb-6 bg-gradient-to-br from-primary via-primary to-primary-deep">
          <div className="bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/20 shadow-lg">
            <div className="flex items-center space-x-3">
              {user?.photoURL ? (
                <div className="relative">
                  <Image
                    src={user.photoURL}
                    alt="User Avatar"
                    width={40}
                    height={40}
                    className="rounded-full border-2 border-white/30"
                  />
                  <div className="absolute -bottom-1 -right-1 w-3 h-3 bg-accent rounded-full border-2 border-white"></div>
                </div>
              ) : (
                <div className="relative">
                  <div className="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center border-2 border-white/30">
                    <span className="text-white font-bold text-sm font-display">
                      {user?.displayName?.charAt(0) || user?.email?.charAt(0) || "U"}
                    </span>
                  </div>
                  <div className="absolute -bottom-1 -right-1 w-3 h-3 bg-accent rounded-full border-2 border-white"></div>
                </div>
              )}
              <div className="flex-1 min-w-0">
                <p className="text-white font-semibold text-sm font-display truncate">
                  {user?.displayName || user?.email?.split('@')[0] || "User"}
                </p>
                <div className="flex items-center space-x-2 mt-1">
                  <div className="px-2 py-0.5 bg-white/20 rounded-md">
                    <span className="text-xs font-medium text-white/90">
                      {user?.role || "User"}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </SidebarHeader>

      <SidebarContent className="bg-gradient-to-b from-primary-deep to-primary px-4">
        <SidebarMenu>
          {filteredNavItems.map((item) => {
            const isActive = pathname === item.href;
            return (
              <SidebarMenuItem key={item.href}>
                <SidebarMenuButton
                  asChild
                  isActive={isActive}
                  className={cn(
                    "text-white/80 hover:text-white hover:bg-white/10 transition-all duration-200",
                    "border border-transparent hover:border-white/20 rounded-lg",
                    "shadow-sm hover:shadow-md",
                    isActive && "bg-white/15 text-white border-white/30 shadow-md"
                  )}
                >
                  <Link href={item.href}>
                    <item.icon className="w-5 h-5" />
                    <span className="font-medium">{item.label}</span>
                  </Link>
                </SidebarMenuButton>
              </SidebarMenuItem>
            );
          })}
        </SidebarMenu>
      </SidebarContent>

      <SidebarFooter className="bg-gradient-to-t from-primary-deep to-primary p-4 space-y-3">
        {/* Language Switcher */}
        <Button
          variant="ghost"
          size="sm"
          onClick={switchLanguage}
          className="w-full justify-start text-white/80 hover:text-white hover:bg-white/10 border border-transparent hover:border-white/20 rounded-lg transition-all duration-200"
        >
          <Globe className="w-4 h-4 mr-2" />
          {locale === "en" ? "العربية" : "English"}
        </Button>

        {/* Sidebar Minimize Button */}
        <SidebarTrigger className="w-full justify-start text-white/80 hover:text-white hover:bg-white/10 border border-transparent hover:border-white/20 rounded-lg transition-all duration-200">
          <PanelLeft className="w-4 h-4 mr-2" />
          <span className="font-medium">Minimize</span>
        </SidebarTrigger>

        {/* Sign Out Button */}
        <Button
          variant="ghost"
          size="sm"
          onClick={handleSignOut}
          className="w-full justify-start text-white/80 hover:text-white hover:bg-white/10 border border-transparent hover:border-white/20 rounded-lg transition-all duration-200"
        >
          <LogOut className="w-4 h-4 mr-2" />
          <span className="font-medium">Sign Out</span>
        </Button>
      </SidebarFooter>
    </Sidebar>
  );
}