"use client";

import { useUser } from "@/hooks/useUser";
import { <PERSON>, <PERSON>Content, Card<PERSON>eader, CardTitle } from "@/components/ui/card";

export default function DashboardPage() {
  const { user } = useUser();

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="border-b border-border pb-4">
        <h1 className="text-3xl font-bold text-charcoal font-display">
          Welcome to DTC Portal
        </h1>
        <p className="text-grey mt-2">
          Hello {user?.displayName || user?.email?.split('@')[0] || "User"}, welcome to your dashboard.
        </p>
      </div>

      {/* Content Area */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <Card className="border-border shadow-sm hover:shadow-md transition-shadow">
          <CardHeader>
            <CardTitle className="text-charcoal font-display">Quick Stats</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-grey">Dashboard content coming soon...</p>
          </CardContent>
        </Card>

        <Card className="border-border shadow-sm hover:shadow-md transition-shadow">
          <CardHeader>
            <CardTitle className="text-charcoal font-display">Recent Activity</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-grey">Activity feed coming soon...</p>
          </CardContent>
        </Card>

        <Card className="border-border shadow-sm hover:shadow-md transition-shadow">
          <CardHeader>
            <CardTitle className="text-charcoal font-display">System Status</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-grey">System monitoring coming soon...</p>
          </CardContent>
        </Card>
      </div>

      {/* Additional Content */}
      <Card className="border-border shadow-sm">
        <CardHeader>
          <CardTitle className="text-charcoal font-display">Getting Started</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <p className="text-grey">
            This is your DTC Portal dashboard. Use the sidebar to navigate between different sections.
          </p>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="p-4 bg-primary/5 rounded-lg border border-primary/20">
              <h3 className="font-semibold text-charcoal mb-2">Home</h3>
              <p className="text-sm text-grey">
                Your main dashboard with overview and quick access to key features.
              </p>
            </div>
            {user?.role === "Admin" && (
              <div className="p-4 bg-accent/5 rounded-lg border border-accent/20">
                <h3 className="font-semibold text-charcoal mb-2">User Management</h3>
                <p className="text-sm text-grey">
                  Manage users, roles, and permissions (Admin only).
                </p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
