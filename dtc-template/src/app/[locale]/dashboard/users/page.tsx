"use client";

import { useUser } from "@/hooks/useUser";
import { useRouter } from "next/navigation";
import { useLocale } from "next-intl";
import { useEffect } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/Card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Users, Shield, UserPlus, Settings } from "lucide-react";

export default function UserManagementPage() {
  const { user } = useUser();
  const router = useRouter();
  const locale = useLocale();

  // Redirect if user is not Admin
  useEffect(() => {
    if (user && user.role !== "Admin") {
      router.push(`/${locale}/dashboard`);
    }
  }, [user, router, locale]);

  if (!user || user.role !== "Admin") {
    return (
      <div className="space-y-6">
        <Alert className="border-destructive/20 bg-destructive/5">
          <Shield className="h-4 w-4" />
          <AlertDescription>
            Access denied. This page is only available to Admin users.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="border-b border-border pb-4">
        <h1 className="text-3xl font-bold text-charcoal font-display">
          User Management
        </h1>
        <p className="text-grey mt-2">
          Manage users, roles, and permissions for the DTC Portal.
        </p>
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card className="border-border shadow-sm hover:shadow-md transition-shadow cursor-pointer">
          <CardContent className="p-6 text-center">
            <Users className="h-8 w-8 text-primary mx-auto mb-2" />
            <h3 className="font-semibold text-charcoal">All Users</h3>
            <p className="text-sm text-grey mt-1">View and manage all users</p>
          </CardContent>
        </Card>

        <Card className="border-border shadow-sm hover:shadow-md transition-shadow cursor-pointer">
          <CardContent className="p-6 text-center">
            <UserPlus className="h-8 w-8 text-accent mx-auto mb-2" />
            <h3 className="font-semibold text-charcoal">Add User</h3>
            <p className="text-sm text-grey mt-1">Create new user account</p>
          </CardContent>
        </Card>

        <Card className="border-border shadow-sm hover:shadow-md transition-shadow cursor-pointer">
          <CardContent className="p-6 text-center">
            <Shield className="h-8 w-8 text-primary-deep mx-auto mb-2" />
            <h3 className="font-semibold text-charcoal">Roles</h3>
            <p className="text-sm text-grey mt-1">Manage user roles</p>
          </CardContent>
        </Card>

        <Card className="border-border shadow-sm hover:shadow-md transition-shadow cursor-pointer">
          <CardContent className="p-6 text-center">
            <Settings className="h-8 w-8 text-grey mx-auto mb-2" />
            <h3 className="font-semibold text-charcoal">Settings</h3>
            <p className="text-sm text-grey mt-1">User management settings</p>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Card className="border-border shadow-sm">
        <CardHeader>
          <CardTitle className="text-charcoal font-display">User Management Dashboard</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <Alert className="border-primary/20 bg-primary/5">
            <Users className="h-4 w-4" />
            <AlertDescription>
              User management features are coming soon. This page will include user listing, 
              role management, and user administration tools.
            </AlertDescription>
          </Alert>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-charcoal">Planned Features</h3>
              <ul className="space-y-2 text-sm text-grey">
                <li className="flex items-center">
                  <div className="w-2 h-2 bg-primary rounded-full mr-3"></div>
                  User listing and search
                </li>
                <li className="flex items-center">
                  <div className="w-2 h-2 bg-primary rounded-full mr-3"></div>
                  Role assignment and management
                </li>
                <li className="flex items-center">
                  <div className="w-2 h-2 bg-primary rounded-full mr-3"></div>
                  User profile editing
                </li>
                <li className="flex items-center">
                  <div className="w-2 h-2 bg-primary rounded-full mr-3"></div>
                  Permission management
                </li>
                <li className="flex items-center">
                  <div className="w-2 h-2 bg-primary rounded-full mr-3"></div>
                  User activity monitoring
                </li>
              </ul>
            </div>

            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-charcoal">Current User Info</h3>
              <div className="p-4 bg-muted/30 rounded-lg border border-border">
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-grey">Name:</span>
                    <span className="text-charcoal font-medium">
                      {user.displayName || "Not set"}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-grey">Email:</span>
                    <span className="text-charcoal font-medium">{user.email}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-grey">Role:</span>
                    <span className="text-charcoal font-medium">{user.role}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-grey">Provider:</span>
                    <span className="text-charcoal font-medium capitalize">{user.provider}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
